package com.afs.azure_finops_sentry.service;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.azure.resourcemanager.compute.ComputeManager;
import com.azure.resourcemanager.compute.models.VirtualMachine;
import com.azure.core.management.AzureEnvironment;
import com.azure.core.management.profile.AzureProfile;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;
import java.util.Map;
import java.util.HashMap;

@Service
public class CostOptimizationService {
    
    private final TokenCredential credential;
    private final String subscriptionId;
    private final String tenantId;
    
    public CostOptimizationService(
            @Value("${azure.subscription-id}") String subscriptionId,
            @Value("${azure.credential.client-id}") String clientId,
            @Value("${azure.credential.client-secret}") String clientSecret,
            @Value("${azure.credential.tenant-id}") String tenantId) {
        this.subscriptionId = subscriptionId;
        this.tenantId = tenantId;
        this.credential = new ClientSecretCredentialBuilder()
                .clientId(clientId)
                .clientSecret(clientSecret)
                .tenantId(tenantId)
                .build();
    }
    
    public Map<String, Object> resizeVM(String resourceId, String newSize) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Get Azure AD token
            var token = credential.getToken(
                new com.azure.core.credential.TokenRequestContext()
                    .addScopes("https://management.azure.com/.default")
            ).block();
            
            // Prepare REST API call to resize VM
            String url = "https://management.azure.com" + resourceId + 
                    "?api-version=2023-07-01";
            
            // Get current VM details first
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getToken());
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map> getResponse = restTemplate.exchange(
                    url, HttpMethod.GET, new HttpEntity<>(headers), Map.class);
            
            Map<String, Object> vmProperties = (Map<String, Object>) getResponse.getBody().get("properties");
            Map<String, Object> hardwareProfile = (Map<String, Object>) vmProperties.get("hardwareProfile");
            
            // Update VM size
            hardwareProfile.put("vmSize", newSize);
            
            // Prepare update request
            Map<String, Object> updateBody = new HashMap<>();
            updateBody.put("properties", vmProperties);
            
            HttpEntity<Map<String, Object>> updateEntity = new HttpEntity<>(updateBody, headers);
            
            ResponseEntity<Map> updateResponse = restTemplate.exchange(
                    url, HttpMethod.PUT, updateEntity, Map.class);
            
            result.put("status", "success");
            result.put("message", "VM " + resourceId + " resized to " + newSize);
            result.put("operation", "resize");
            result.put("newSize", newSize);
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", "Failed to resize VM: " + e.getMessage());
        }
        
        return result;
    }
    
    public Map<String, Object> deleteResource(String resourceId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Get Azure AD token
            var token = credential.getToken(
                new com.azure.core.credential.TokenRequestContext()
                    .addScopes("https://management.azure.com/.default")
            ).block();
            
            // Prepare REST API call to delete resource
            String url = "https://management.azure.com" + resourceId + 
                    "?api-version=2023-07-01";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getToken());
            
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map> response = restTemplate.exchange(
                    url, HttpMethod.DELETE, new HttpEntity<>(headers), Map.class);
            
            result.put("status", "success");
            result.put("message", "Resource " + resourceId + " deleted successfully");
            result.put("operation", "delete");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", "Failed to delete resource: " + e.getMessage());
        }
        
        return result;
    }
    
    public Map<String, Object> enableAutoShutdown(String resourceId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Get Azure AD token
            var token = credential.getToken(
                new com.azure.core.credential.TokenRequestContext()
                    .addScopes("https://management.azure.com/.default")
            ).block();
            
            // Prepare REST API call to enable auto-shutdown
            String url = "https://management.azure.com" + resourceId + 
                    "/providers/microsoft.devtestlab/schedules/autoShutdown?api-version=2018-09-15";
            
            Map<String, Object> scheduleBody = new HashMap<>();
            scheduleBody.put("properties", Map.of(
                "timeZoneId", "UTC",
                "dailyRecurrence", Map.of("time", "18:00"),
                "taskType", "ComputeVmShutdownTask",
                "notificationSettings", Map.of(
                    "status", "Enabled",
                    "timeInMinutes", 30
                )
            ));
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getToken());
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map> response = restTemplate.exchange(
                    url, HttpMethod.PUT, new HttpEntity<>(scheduleBody, headers), Map.class);
            
            result.put("status", "success");
            result.put("message", "Auto-shutdown enabled for " + resourceId);
            result.put("operation", "autoShutdown");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", "Failed to enable auto-shutdown: " + e.getMessage());
        }
        
        return result;
    }
}
