package com.afs.azure_finops_sentry.service;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Service
public class CostPredictionService {
    
    @Autowired
    private CostManagementService costManagementService;
    
    public Map<String, Object> predictCostOverrun(Map<String, Object> costData) {
        Map<String, Object> prediction = new HashMap<>();
        
        try {
            // Extract current costs
            Double vmCost = getDoubleValue(costData.get("vmCost"));
            Double storageCost = getDoubleValue(costData.get("storageCost"));
            Double totalCost = vmCost + storageCost;
            
            // Get historical data for trend analysis
            Map<String, Object> historicalCosts = getHistoricalCosts();
            
            // Simple prediction algorithm (can be enhanced with ML)
            Double predictedCost = calculatePredictedCost(totalCost, historicalCosts);
            Double budget = 1000.0; // Default budget, should be configurable
            
            boolean isOverrun = predictedCost > budget;
            Double overrunAmount = isOverrun ? predictedCost - budget : 0.0;
            Double overrunPercentage = isOverrun ? (overrunAmount / budget) * 100 : 0.0;
            
            prediction.put("currentCost", totalCost);
            prediction.put("predictedCost", predictedCost);
            prediction.put("budget", budget);
            prediction.put("isOverrun", isOverrun);
            prediction.put("overrunAmount", overrunAmount);
            prediction.put("overrunPercentage", overrunPercentage);
            prediction.put("recommendations", generateRecommendations(vmCost, storageCost, predictedCost, budget));
            prediction.put("status", "success");
            
        } catch (Exception e) {
            prediction.put("status", "error");
            prediction.put("message", "Failed to predict cost overrun: " + e.getMessage());
        }
        
        return prediction;
    }
    
    private Double getDoubleValue(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                return 0.0;
            }
        }
        return 0.0;
    }
    
    private Map<String, Object> getHistoricalCosts() {
        // This would fetch historical cost data from Azure Cost Management API
        // For now, return mock data
        Map<String, Object> historical = new HashMap<>();
        historical.put("lastWeek", 850.0);
        historical.put("lastMonth", 3200.0);
        historical.put("trend", "increasing");
        return historical;
    }
    
    private Double calculatePredictedCost(Double currentCost, Map<String, Object> historical) {
        // Simple prediction: assume 10% increase based on trend
        Double lastWeek = getDoubleValue(historical.get("lastWeek"));
        Double trend = currentCost > lastWeek ? 1.1 : 0.95; // 10% increase or 5% decrease
        return currentCost * trend;
    }
    
    private String[] generateRecommendations(Double vmCost, Double storageCost, Double predictedCost, Double budget) {
        java.util.List<String> recommendations = new java.util.ArrayList<>();
        
        if (predictedCost > budget) {
            recommendations.add("Consider downsizing VMs to reduce compute costs");
            recommendations.add("Review and delete unused storage resources");
            recommendations.add("Enable auto-shutdown for non-production VMs");
            recommendations.add("Consider reserved instances for predictable workloads");
        }
        
        if (vmCost > storageCost * 2) {
            recommendations.add("VM costs are significantly higher than storage costs - optimize VM sizing");
        }
        
        if (storageCost > vmCost) {
            recommendations.add("Storage costs are high - review data retention policies");
        }
        
        return recommendations.toArray(new String[0]);
    }
} 