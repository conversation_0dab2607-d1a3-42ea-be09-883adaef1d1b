<component name="libraryTable">
  <library name="Maven: com.microsoft.azure:msal4j-persistence-extension:1.3.0">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/com/microsoft/azure/msal4j-persistence-extension/1.3.0/msal4j-persistence-extension-1.3.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/com/microsoft/azure/msal4j-persistence-extension/1.3.0/msal4j-persistence-extension-1.3.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/com/microsoft/azure/msal4j-persistence-extension/1.3.0/msal4j-persistence-extension-1.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>