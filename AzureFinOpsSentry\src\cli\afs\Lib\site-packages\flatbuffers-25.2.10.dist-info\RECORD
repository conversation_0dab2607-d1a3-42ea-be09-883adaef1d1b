flatbuffers-25.2.10.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
flatbuffers-25.2.10.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
flatbuffers-25.2.10.dist-info/METADATA,sha256=MEsu9YBw74eGEWK8g0wRR3kfholSQAeHW0Pi9WbFzpI,875
flatbuffers-25.2.10.dist-info/RECORD,,
flatbuffers-25.2.10.dist-info/WHEEL,sha256=Kh9pAotZVRFj97E15yTA4iADqXdQfIVTHcNaZTjxeGM,110
flatbuffers-25.2.10.dist-info/top_level.txt,sha256=UXVWLA8ys6HeqTz6rfKesocUq6ln-ZL8mhZC_cq5BEc,12
flatbuffers/__init__.py,sha256=q7t27Nzut2qVnujPIRZgrk3HObmS22YTPmuW95BF5EY,751
flatbuffers/__pycache__/__init__.cpython-39.pyc,,
flatbuffers/__pycache__/_version.cpython-39.pyc,,
flatbuffers/__pycache__/builder.cpython-39.pyc,,
flatbuffers/__pycache__/compat.cpython-39.pyc,,
flatbuffers/__pycache__/encode.cpython-39.pyc,,
flatbuffers/__pycache__/flexbuffers.cpython-39.pyc,,
flatbuffers/__pycache__/number_types.cpython-39.pyc,,
flatbuffers/__pycache__/packer.cpython-39.pyc,,
flatbuffers/__pycache__/table.cpython-39.pyc,,
flatbuffers/__pycache__/util.cpython-39.pyc,,
flatbuffers/_version.py,sha256=1mWqRAzC1EI5JPOnv4c38HVDiJk_NCL08u82MSFqcIY,696
flatbuffers/builder.py,sha256=k6veSkosKvRhgVJwva7VpOiRK5StEAxwtEUjlV5_u9M,27472
flatbuffers/compat.py,sha256=2xP9x1ovX-IdPeayHIwr_dJpQvJwugRPN1wK5vj7XuU,2519
flatbuffers/encode.py,sha256=SMrGb1y8_EccMm5vMU1PjgAhb9_NHTiFkj83kNI7dng,1581
flatbuffers/flexbuffers.py,sha256=FmXELtTvWdfwigs6IYiGp9Xya-QDiUY4Uc4g4jv0AuY,44275
flatbuffers/number_types.py,sha256=8IKuKo822v4gQvUOO_CDCuJfg4diGBOLOBj5kLV7dPo,3955
flatbuffers/packer.py,sha256=pw3y_NM-WOEcbfT6NzACszU9PPESQYDZabofgRxsj-g,1165
flatbuffers/table.py,sha256=8OmekiqEywQ_pMqT7Q4UTJyMCU8eVhABKplrKSCNQhk,5170
flatbuffers/util.py,sha256=Ib1cSx9fsgF9BY84iYl0Tjj7qftNhniI7ZEWyJwvZGE,1669
