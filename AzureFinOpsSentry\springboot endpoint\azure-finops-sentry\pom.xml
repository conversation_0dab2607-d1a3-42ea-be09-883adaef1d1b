<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.3</version>
		<relativePath/>
	</parent>
	<groupId>com.afs</groupId>
	<artifactId>azure-finops-sentry</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>azure-finops-sentry</name>
	<description>FinOps project with Spring Boot</description>
	<properties>
		<java.version>17</java.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.azure.spring</groupId>
				<artifactId>spring-cloud-azure-dependencies</artifactId>
				<version>5.22.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.azure</groupId>
				<artifactId>azure-sdk-bom</artifactId>
				<version>1.2.24</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- Azure Identity for authentication -->
		<dependency>
			<groupId>com.azure</groupId>
			<artifactId>azure-identity</artifactId>
			<version>1.16.2</version>
		</dependency>
		<!-- Azure Resource Manager for managing Azure resources -->
		<dependency>
			<groupId>com.azure.resourcemanager</groupId>
			<artifactId>azure-resourcemanager-resources</artifactId>
			<version>2.42.0</version>
		</dependency>
		<!-- Azure Cost Management for cost queries -->
		<dependency>
			<groupId>com.azure.resourcemanager</groupId>
			<artifactId>azure-resourcemanager-consumption</artifactId>
			<version>1.0.0-beta.4</version>
		</dependency>

		<dependency>
			<groupId>com.azure</groupId>
			<artifactId>azure-core-http-netty</artifactId>
			<version>1.15.7</version>
		</dependency>

		<!-- Azure Key Vault Secrets (if needed for credential storage) -->
		<dependency>
			<groupId>com.azure</groupId>
			<artifactId>azure-security-keyvault-secrets</artifactId>
			<version>4.10.0</version>
		</dependency>
		<!-- Dotenv for environment variable loading -->
		<dependency>
			<groupId>io.github.cdimascio</groupId>
			<artifactId>java-dotenv</artifactId>
			<version>5.2.2</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>