name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache Maven packages
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: Build and Test Backend
      run: |
        cd "springboot endpoint/azure-finops-sentry"
        mvn clean test
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install Python dependencies
      run: |
        cd src/cli
        pip install -r requirements.txt
    
    - name: Test CLI
      run: |
        cd src/cli
        python -m pytest tests/ || echo "No tests found"

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Build JAR
      run: |
        cd "springboot endpoint/azure-finops-sentry"
        mvn clean package -DskipTests
    
    - name: Upload JAR artifact
      uses: actions/upload-artifact@v4
      with:
        name: azure-finops-sentry-jar
        path: springboot endpoint/azure-finops-sentry/target/azure-finops-sentry-*.jar

  docker:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./springboot endpoint/azure-finops-sentry
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/azure-finops-sentry:latest
          ${{ secrets.DOCKER_USERNAME }}/azure-finops-sentry:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max 