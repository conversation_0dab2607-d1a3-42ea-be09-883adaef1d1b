package com.afs.azure_finops_sentry.controller;

import com.afs.azure_finops_sentry.service.CostManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class CostController {
    private final CostManagementService costService;

    @Autowired
    public CostController(CostManagementService costService) {
        this.costService = costService;
    }

    @GetMapping("/costs")
    public Map<String, Object> getCosts() {
        return costService.getCosts();
    }
}