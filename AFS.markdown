# CLI-based FinOps-Driven Cost Optimizer on Azure

**GitHub Repo Name**: Azure-FinOps-Sentry

## Project Overview
**Azure-FinOps-Sentry** is an open-source CLI tool designed to monitor, predict, and optimize Azure cloud costs using AI-driven analytics and automation. Built with Java Spring Boot, it leverages Azure Cost Management for real-time cost data, a Random Forest model for predicting cost overruns, and Azure Functions for automated cost-saving actions (e.g., resizing VMs, deleting unused resources). Secured with Azure Active Directory (AAD), Virtual Network (VNet), Network Security Groups (NSGs), and Key Vault, it’s tailored for DevOps teams to minimize cloud expenses. The project is portable, allowing users to deploy it with their own Azure credentials, ensuring functionality after your Azure for Students account expires. It fits a $47 budget and is completable in 3 months with ~10-15 hours/week, ideal for a 4th-year student targeting Microsoft Azure roles in FinOps or DevOps.

## Objectives
- Develop a CLI tool to monitor Azure costs, predict overruns, and automate optimizations using AI and MCP.
- Train an AI model for cost anomaly detection, exportable as ONNX for local use.
- Build a Spring Boot backend with MCP for AI-Azure API integration.
- Secure with AAD, VNet, NSGs, and Key Vault.
- Ensure portability with user-configurable credentials and local testing.
- Stay within $47 budget using Azure free tiers.
- Deliver an open-source tool for DevOps teams to optimize cloud costs.

## Target Audience
- Intermediate developers with Python, Java/Spring Boot, and Azure skills.
- Students targeting Microsoft Azure roles in FinOps/DevOps.
- IT teams needing proactive cloud cost management.

## Prerequisites
- Azure for Students account with $47 (for development).
- Knowledge of Python, Java/Spring Boot, Azure CLI, basic AI.
- Local setup: Java 17, Maven, Python 3.8+, Azure CLI, IDE (e.g., IntelliJ).
- Users: Any Azure account with a Service Principal for deployment.

## Post-Expiration Strategy
- **User Credentials**: Users configure their own Azure Service Principal in the CLI and backend, ensuring no dependency on your account.
- **Local Simulation**: Provide mock Azure API responses and pre-trained ONNX model for local testing without an active Azure subscription.
- **Documentation**: Detailed README with setup scripts for users to deploy in their Azure environments.
- **Model Portability**: Export AI model as ONNX, allowing users to run predictions locally or in other clouds.

## Azure Services Used
- **Azure Cost Management**: Cost and usage data (free tier, 5 GB/month queries).
- **Azure Machine Learning**: AI model training (free tier).
- **Azure Functions**: Cost-saving actions (free tier, 1M executions/month).
- **Azure VNet/NSGs**: Secure communication (~$0.50-$1/month).
- **Azure Key Vault**: Credentials (free tier, 10,000 operations/month).
- **Azure Blob Storage**: Logs/reports (~$0.02/GB/month).
- **Azure Container Apps**: Hosts Spring Boot (free tier, 0.5 vCPU, 1 GB RAM).
- **Azure AD**: IAM (free tier for RBAC).

## Estimated Costs (Your Development)
- **Total**: ~$1-$2/month, fitting $47 for 3 months.
- **Cost Management**:
  - Budget alert: `az costmanagement budget create --name BudgetAlert --amount 5 --time-grain Monthly --resource-group FinOpsRG`.
  - Deallocate resources: `az group delete --name FinOpsRG --no-wait`.
  - Monitor in Azure Portal.

## Project Architecture
1. **CLI Tool**: Python CLI for cost monitoring, overrun prediction, and optimization actions.
2. **Spring Boot Backend**: Hosts MCP server, Azure API integration.
3. **Azure Cost Management**: Collects cost and usage data (e.g., VM, storage costs).
4. **Azure ML**: Trains Random Forest model, exported as ONNX.
5. **MCP**: Links AI predictions to cost-saving actions.
6. **Azure Functions**: Executes serverless optimization tasks.
7. **Azure Blob Storage**: Stores logs/reports.
8. **Azure VNet/NSGs**: Secures communication.
9. **Azure Key Vault**: Manages API keys.
10. **Azure AD**: Enforces IAM.

## Tasks and Implementation Steps (~10-15 hours/week, 3 months)

### 1. Set Up Azure Environment and Local Simulation (Weeks 1-2, ~10 hours)
- **Tasks**:
  - Create resource group: `az group create --name FinOpsRG --location eastus`.
  - Set up VNet: `az network vnet create --resource-group FinOpsRG --name FinOpsVNet --address-prefixes 10.0.0.0/16`.
  - Configure NSGs: `az network nsg create --resource-group FinOpsRG --name FinOpsNSG`.
  - Deploy test VM: `az vm create --resource-group FinOpsRG --name TestVM --image UbuntuLTS --size Standard_B1s`.
  - Enable Azure Cost Management: `az costmanagement query create --name FinOpsQuery --resource-group FinOpsRG`.
  - Deploy Key Vault: `az keyvault create --name FinOpsVault --resource-group FinOpsRG`.
  - Create storage: `az storage account create --name finopsstorage --resource-group FinOpsRG --sku Standard_LRS`.
  - Set IAM: `az ad sp create-for-rbac --name FinOpsSP --role Contributor --scopes /subscriptions/<subscription-id>/resourceGroups/FinOpsRG`.
  - Create mock API responses for local testing:
    ```python
    # mocks/azure_costmanagement_mock.py
    def get_costs(resource_group, start_date, end_date):
        return {"vm1": 5.0, "storage": 2.0}  # Simulated cost data
    ```
  - Store Service Principal in Key Vault: `az keyvault secret set --vault-name FinOpsVault --name AzureClientId --value <client-id>`.
- **Skills**: Azure CLI, VNet/NSGs, Azure Cost Management, Key Vault, IAM.

### 2. Train and Export AI Model (Weeks 3-5, ~15 hours)
- **Tasks**:
  - Train Random Forest model in Azure ML on simulated cost data (e.g., VM costs, storage usage).
  - Export to ONNX: `az storage blob upload --account-name finopsstorage --container-name models --name cost_model.onnx`.
  - Provide pre-trained ONNX model in repo for local use.
  - Test locally:
    ```python
    import onnxruntime as ort
    import numpy as np
    session = ort.InferenceSession("cost_model.onnx")
    inputs = np.array([[5.0, 2.0]], dtype=np.float32)  # Simulated VM, storage costs
    outputs = session.run(None, {"input": inputs})[0]
    print(f"Cost overrun probability: {outputs}")
    ```
- **Skills**: Azure ML, Random Forest, ONNX.

### 3. Develop Spring Boot Backend with MCP (Weeks 6-9, ~20 hours)
- **Tasks**:
  - Create Spring Boot project:
    ```xml
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-identity</artifactId>
            <version>1.15.4</version>
        </dependency>
        <dependency>
            <groupId>com.azure.resourcemanager</groupId>
            <artifactId>azure-resourcemanager</artifactId>
            <version>2.42.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-mcp</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    ```
  - Configure MCP:
    ```java
    @SpringBootApplication
    public class FinOpsApplication {
        public static void main(String[] args) {
            SpringApplication.run(FinOpsApplication.class, args);
        }
        @Bean
        public ToolCallbackProvider tools(CostOptimizationTools costTools) {
            return MethodToolCallbackProvider.builder()
                    .toolObjects(costTools)
                    .build();
        }
    }
    ```
  - Implement CostOptimizationTools:
    ```java
    public class CostOptimizationTools {
        @Autowired
        private ComputeManager computeManager;
        @ToolCallback(description = "Resize VM")
        public String resizeVM(@ToolArg(description = "Resource ID") String resourceId,
                              @ToolArg(description = "Size") String size) {
            computeManager.virtualMachines().getById(resourceId).update().withSize(size).apply();
            return "Resized " + resourceId + " to " + size;
        }
        @ToolCallback(description = "Delete resource")
        public String deleteResource(@ToolArg(description = "Resource ID") String resourceId) {
            computeManager.virtualMachines().deleteById(resourceId);
            return "Deleted " + resourceId;
        }
    }
    ```
  - Configure `application.yml` for user credentials:
    ```yaml
    spring:
      ai:
        mcp:
          server:
            name: finops-mcp-server
            version: 1.0.0
      server:
        port: 8080
    azure:
      credential:
        client-id: ${AZURE_CLIENT_ID}
        client-secret: ${AZURE_CLIENT_SECRET}
        tenant-id: ${AZURE_TENANT_ID}
      keyvault:
        uri: ${AZURE_KEYVAULT_URI}
    ```
  - Deploy to Azure Container Apps: `az containerapp create --name finops-app --resource-group FinOpsRG --environment finops-env`.
  - Provide user setup script:
    ```bash
    # scripts/setup.sh
    az ad sp create-for-rbac --name AzureFinOpsSentrySP --role Contributor --scopes /subscriptions/<user-subscription-id>
    ```
- **Skills**: Spring Boot, MCP, Azure SDK, Container Apps.

### 4. Integrate AI and Azure APIs via MCP (Weeks 10-11, ~15 hours)
- **Tasks**:
  - Create MCP prompt:
    ```java
    @Bean
    public List<McpServerFeatures.SyncPromptRegistration> prompts() {
        var prompt = new McpSchema.Prompt("predict-overrun", "Predict cost overrun", List.of(
                new McpSchema.PromptArgument("resourceGroup", "Resource Group", true),
                new McpSchema.PromptArgument("costs", "Cost data", true)
        ));
        var promptRegistration = new McpServerFeatures.SyncPromptRegistration(prompt, request -> {
            String resourceGroup = (String) request.arguments().get("resourceGroup");
            String costs = (String) request.arguments().get("costs");
            String prediction = callAIModel(costs); // Call ONNX model
            return new McpSchema.GetPromptResult("Prediction for " + resourceGroup, List.of(
                    new McpSchema.PromptMessage(McpSchema.Role.USER, new McpSchema.TextContent(prediction))
            ));
        });
        return List.of(promptRegistration);
    }
    ```
  - Implement AI model call:
    ```java
    public String callAIModel(String costs) {
        // Load ONNX model locally or from Blob Storage
        // Return prediction (e.g., "Predicted overrun: $10")
        return "Predicted overrun: $10 (50% above budget)"; // Placeholder
    }
    ```
  - Use Azure SDK for optimization:
    ```java
    import com.azure.resourcemanager.compute.ComputeManager;
    @Autowired
    private ComputeManager computeManager;
    public void resizeVM(String resourceId, String size) {
        computeManager.virtualMachines().getById(resourceId).update().withSize(size).apply();
    }
    ```
- **Skills**: MCP, Azure SDK, Key Vault.

### 5. Develop CLI Tool (Weeks 12-13, ~10 hours)
- **Tasks**:
  - Build Python CLI with `click`:
    ```python
    import click
    import requests
    from azure.identity import ClientSecretCredential
    from azure.mgmt.costmanagement import CostManagementClient
    import json

    @click.group()
    @click.option('--config', default='config.json', help='Path to Azure config file')
    @click.pass_context
    def cli(ctx, config):
        with open(config) as f:
            ctx.obj = json.load(f)  # Load user Azure credentials

    @click.command()
    @click.option('--resource-group', required=True)
    @click.option('--start-date', required=True)
    @click.option('--end-date', required=True)
    @click.pass_context
    def analyze_costs(ctx, resource_group, start_date, end_date):
        credential = ClientSecretCredential(
            tenant_id=ctx.obj['tenant_id'],
            client_id=ctx.obj['client_id'],
            client_secret=ctx.obj['client_secret']
        )
        client = CostManagementClient(credential, ctx.obj['subscription_id'])
        costs = {"resourceGroup": resource_group, "costs": {"vm1": 5.0, "storage": 2.0}}  # Replace with real query
        response = requests.post("http://localhost:8080/predict-overrun", json=costs)
        click.echo(response.json())

    @click.command()
    @click.option('--resource-id', required=True)
    @click.option('--action', required=True, help='Action (resize/delete)')
    @click.option('--size', default='Standard_B1s', help='VM size (e.g., Standard_B1s)')
    def optimize(resource_id, action, size):
        payload = {"resourceId": resource_id, "action": action}
        if action == "resize":
            payload["size"] = size
        response = requests.post("http://localhost:8080/optimize", json=payload)
        click.echo(response.json())

    cli.add_command(analyze_costs)
    cli.add_command(optimize)
    if __name__ == '__main__':
        cli()
    ```
  - Create `config.json` template for users:
    ```json
    {
        "subscription_id": "<user-subscription-id>",
        "tenant_id": "<user-tenant-id>",
        "client_id": "<user-client-id>",
        "client_secret": "<user-client-secret>",
        "keyvault_uri": "<user-keyvault-uri>"
    }
    ```
  - Package CLI: `python setup.py sdist bdist_wheel`.
- **Skills**: Python CLI, Azure AD.

### 6. Secure, Document, and Open-Source (Week 14, ~5 hours)
- **Tasks**:
  - Restrict NSG rules to Spring Boot and Azure APIs.
  - Fine-tune IAM: `az role assignment create --assignee <user-client-id> --role "Cost Management Reader" --resource-group FinOpsRG`.
  - Set alerts: `az monitor metrics alert create --name CostAlert --resource-group FinOpsRG --condition "total cost > 5"`.
  - Create README (this file).
  - Add setup script, pre-trained ONNX model, and MIT license to repo.
  - Publish to GitHub: `git push origin main`.
  - Clean up: `az group delete --name FinOpsRG --no-wait`.
- **Skills**: IAM, NSGs, open-source practices.

## Post-Expiration Usability
- **User Deployment**: Users provide their Azure Service Principal credentials in `config.json` to access their Azure resources.
- **Local Testing**: Mock APIs and pre-trained ONNX model allow users to test without an Azure subscription.
- **Documentation**: README and scripts guide users to set up their own Azure environment.
- **Portability**: ONNX model and Spring Boot backend can run locally or in other clouds.

## Skills Acquired
- **AI**: Random Forest, Azure ML, MCP.
- **FinOps**: Cost optimization, Azure Cost Management.
- **Cloud**: Azure CLI, Container Apps, Functions.
- **Security**: IAM, Key Vault, VNet, NSGs.
- **Backend**: Spring Boot, MCP, REST APIs.

## Use Case
- **Application**: Monitors and optimizes Azure costs, reducing overspending for DevOps teams.
- **Relevance**: Aligns with Microsoft’s FinOps focus, critical for 2025 cloud management.
- **Uniqueness**: Novel AI-driven FinOps tool, distinct from commercial solutions like Azure Cost Management.
- **Resume Value**: Showcases Azure expertise for Microsoft roles in FinOps/DevOps.

## Current Progress (as of July 1, 2025)
- **Spring Boot Backend**:
  - Set up project with Maven, Java 17, and Spring Boot 3.2.1.
  - Added dependencies: `spring-boot-starter-web`, `azure-identity` (1.15.4), `azure-resourcemanager` (2.42.0).
  - Implemented mock APIs:
    - `/costs`: Simulates Azure Cost Management data (e.g., VM: $5, storage: $2).
    - `/optimize`: Simulates cost-saving actions (e.g., VM resize, resource deletion).
    - `/predict-overrun`: Simulates AI predictions for cost anomalies.
  - Fixed bean not found error for `MockCostManagementService` in `CostController`.
- **Python CLI**:
  - Created CLI skeleton using `click` and `requests`.
  - Implemented commands: `analyze-costs` (fetches costs) and `optimize` (triggers actions).
  - Configured mock `config.json` for local testing.
- **GitHub**:
  - Initialized repository: `Azure-FinOps-Sentry`.
  - Committed initial code, including mock APIs and CLI.

## Challenges and Solutions
- **Challenge**: Limited availability.
  - **Solution**: Modular tasks fit ~10-15 hours/week.
- **Challenge**: MCP learning curve.
  - **Solution**: Use Spring AI’s MCP docs; mock APIs as fallback.
- **Challenge**: Post-expiration functionality.
  - **Solution**: User-configurable credentials, local testing with mocks.
- **Challenge**: Budget constraints ($47).
  - **Solution**: Use free-tier services, local simulation, budget alerts.

## Resume Example
**Azure-FinOps-Sentry (CLI-based FinOps Cost Optimizer)**  
- Built an open-source CLI and Spring Boot backend using MCP to monitor, predict, and optimize Azure costs with AI.  
- Integrated Azure Cost Management, ML, and Functions, secured with IAM, VNet, and Key Vault, portable for user deployment.  
- Optimized for $47 budget, showcasing FinOps, Azure, and security skills for Microsoft roles.  
- Skills: Spring Boot, MCP, Azure ML, Azure CLI, IAM, FinOps.

## Resources
- [Spring AI MCP](https://docs.spring.io/spring-ai/reference/mcp.html)
- [Azure Cost Management](https://learn.microsoft.com/en-us/azure/cost-management-billing/)
- [Azure Machine Learning](https://learn.microsoft.com/en-us/azure/machine-learning/)
- [Azure SDK for Java](https://learn.microsoft.com/en-us/azure/developer/java/sdk/)

## Notes
- Test locally with mocks to save costs.
- Engage Azure communities on GitHub/LinkedIn.
- Extend with more optimization actions (e.g., scheduling VMs) if time allows.



<!-- Brief description -->

Project Overview: Azure-FinOps-Sentry

Goal:
Build an open-source tool to help DevOps teams monitor, predict and optimize Azure cloud costs using AI and automation.

Main Components:
Spring Boot Backend (Java):
Exposes REST APIs for cost monitoring, prediction, and optimization.
Integrates with Azure Cost Management APIs to fetch real-time cost data.
Uses a mock or real AI model (ONNX) to predict cost overruns.
Can trigger cost-saving actions (e.g. resize/delete VMs).

Python CLI Tool:
Lets users fetch costs, get predictions, and optimize resources from the command line.
Talks to the Spring Boot backend via HTTP.

Azure Integration:
Uses Azure SDKs for authentication and resource management.
Supports secure credential storage (Key Vault), IAM and network security (VNet, NSGs).
Designed to be portable: users can plug in their own Azure credentials.

Key Features:
Cost Monitoring:
Fetches and displays Azure resource costs (VMs, storage, etc.).
Cost Prediction:
Uses an AI model to predict if you’ll exceed your budget.
Optimization Actions:
Automates actions like resizing or deleting resources to save money.
Security:
Uses Azure AD, Key Vault, and network security best practices.
Portability:
Works with any Azure account; can be tested locally with mock data.

Tech Stack:
Backend: Java 17, Spring Boot, Azure SDKs, ONNX (for AI model)
CLI: Python (Click, Requests, Azure SDK)
Azure Services: Cost Management, Key Vault, Container Apps, AD, etc.

Target Users:
Students, DevOps engineers, and IT teams who want to proactively manage and optimize Azure cloud costs.