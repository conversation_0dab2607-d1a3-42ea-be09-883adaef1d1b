<component name="libraryTable">
  <library name="Maven: io.projectreactor.netty:reactor-netty-core:1.2.7">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/io/projectreactor/netty/reactor-netty-core/1.2.7/reactor-netty-core-1.2.7-sources.jar!/" />
    </SOURCES>
  </library>
</component>