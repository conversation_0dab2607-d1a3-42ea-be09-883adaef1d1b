spring.application.name=azure-finops-sentry

# Server Configuration
server.port=8080
server.servlet.context-path=/api

# Azure Configuration
azure.subscription-id=${AZURE_SUBSCRIPTION_ID:a2bbd88a-3a9a-4992-9efc-4275c419622d}
azure.credential.client-id=${AZURE_CLIENT_ID:01ac2a43-5164-4803-9563-0534169d4e36}
azure.credential.client-secret=${AZURE_CLIENT_SECRET:****************************************}
azure.credential.tenant-id=${AZURE_TENANT_ID:1af34a08-3cc1-4117-8dbf-67da10c0e4c6}

# FinOps Configuration
finops.budget.default=1000.0
finops.budget.monthly=5000.0
finops.alert.threshold=0.8
finops.optimization.enabled=true
finops.auto-shutdown.enabled=true
finops.auto-shutdown.time=18:00

# Logging Configuration
logging.level.com.afs.azure_finops_sentry=INFO
logging.level.org.springframework.web=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always
