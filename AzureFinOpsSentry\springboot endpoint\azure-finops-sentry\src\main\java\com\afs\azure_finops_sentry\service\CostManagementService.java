package com.afs.azure_finops_sentry.service;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.azure.core.credential.AccessToken;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
public class CostManagementService {
    private final TokenCredential credential;
    private final String subscriptionId;
    private final String tenantId;
    private final String clientId;

    public CostManagementService(
            @Value("${azure.subscription-id}") String subscriptionId,
            @Value("${azure.credential.client-id}") String clientId,
            @Value("${azure.credential.client-secret}") String clientSecret,
            @Value("${azure.credential.tenant-id}") String tenantId) {
        this.subscriptionId = subscriptionId;
        this.tenantId = tenantId;
        this.clientId = clientId;
        this.credential = new ClientSecretCredentialBuilder()
                .clientId(clientId)
                .clientSecret(clientSecret)
                .tenantId(tenantId)
                .build();
    }

    public Map<String, Object> getCosts() {
        Map<String, Object> costs = new HashMap<>();
        try {
            // Get Azure AD token for the Cost Management API
            AccessToken token = credential.getToken(
                new com.azure.core.credential.TokenRequestContext()
                    .addScopes("https://management.azure.com/.default")
            ).block();

            // Prepare REST API call
            String url = "https://management.azure.com/subscriptions/" + subscriptionId +
                    "/providers/Microsoft.CostManagement/query?api-version=2023-03-01";

            // Build request body for cost query
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("type", "Usage");
            requestBody.put("timeframe", "TheLast7Days");
            
            // Add time period
            Map<String, Object> timePeriod = new HashMap<>();
            timePeriod.put("from", OffsetDateTime.now().minusDays(7).toString());
            timePeriod.put("to", OffsetDateTime.now().toString());
            requestBody.put("timePeriod", timePeriod);

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(token.getToken());
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, Map.class);

            costs.put("result", response.getBody());
            costs.put("status", "success");
        } catch (Exception e) {
            costs.put("error", "Failed to fetch costs: " + e.getMessage());
            costs.put("status", "error");
        }
        return costs;
    }
}