package com.afs.azure_finops_sentry.controller;

import com.afs.azure_finops_sentry.service.CostOptimizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class OptimizationController {
    @Autowired
    private CostOptimizationService optimizationService;

    @PostMapping("/optimize")
    public Map<String, Object> optimize(@RequestBody Map<String, String> request) {
        String action = request.get("action");
        String resourceId = request.get("resourceId");
        
        switch (action) {
            case "resize":
                String size = request.getOrDefault("size", "Standard_B1s");
                return optimizationService.resizeVM(resourceId, size);
            case "delete":
                return optimizationService.deleteResource(resourceId);
            case "autoShutdown":
                return optimizationService.enableAutoShutdown(resourceId);
            default:
                Map<String, Object> error = new java.util.HashMap<>();
                error.put("status", "error");
                error.put("message", "Invalid action: " + action);
                return error;
        }
    }
}
