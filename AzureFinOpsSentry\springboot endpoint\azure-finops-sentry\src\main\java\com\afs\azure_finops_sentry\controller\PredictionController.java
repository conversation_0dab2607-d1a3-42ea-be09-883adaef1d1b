package com.afs.azure_finops_sentry.controller;

import com.afs.azure_finops_sentry.service.CostPredictionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class PredictionController {
    @Autowired
    private CostPredictionService predictionService;

    @PostMapping("/predict-overrun")
    public Map<String, Object> predict(@RequestBody Map<String, Object> costData) {
        return predictionService.predictCostOverrun(costData);
    }
}

